import { createClient } from '@supabase/supabase-js';

// Configuración de Supabase - Proyecto Inventario
const supabaseUrl = 'https://lwsmzajcafckotboifiy.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imx3c216YWpjYWZja290Ym9pZml5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MTcxNjY2OTUsImV4cCI6MjAzMjc0MjY5NX0.4Hs8KqJ5wZ9QJ7X8KqJ5wZ9QJ7X8KqJ5wZ9QJ7X8KqJ';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  }
});

// Tipos para la base de datos
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          name: string;
          role: 'usuario' | 'administrador' | 'super_administrador';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          role?: 'usuario' | 'administrador' | 'super_administrador';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          role?: 'usuario' | 'administrador' | 'super_administrador';
          created_at?: string;
          updated_at?: string;
        };
      };
    };
  };
}

export type UserRole = 'usuario' | 'administrador' | 'super_administrador';

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  created_at: string;
  updated_at: string;
}
