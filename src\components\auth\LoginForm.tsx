import React, { useState, useEffect } from 'react';
import type { ChangeEvent, FormEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LoginFormProps {
  onSubmit: (email: string, password: string) => void;
  onSwitchToRegister: () => void;
}

const EmailIcon = () => (
  <svg className="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 4.375H2.5C2.15833 4.375 1.875 4.65833 1.875 5V15C1.875 15.3417 2.15833 15.625 2.5 15.625H17.5C17.8417 15.625 18.125 15.3417 18.125 15V5C18.125 4.65833 17.8417 4.375 17.5 4.375Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.125 5.625L10 10.625L16.875 5.625" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const PasswordIcon = () => (
  <svg className="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.625 9.375H4.375C3.68464 9.375 3.125 9.93464 3.125 10.625V16.25C3.125 16.9404 3.68464 17.5 4.375 17.5H15.625C16.3154 17.5 16.875 16.9404 16.875 16.25V10.625C16.875 9.93464 16.3154 9.375 15.625 9.375Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.625 9.375V5.625C5.625 4.63044 6.01339 3.67661 6.70352 2.98648C7.39365 2.29635 8.34748 1.90625 9.34204 1.90625H10.658C11.6525 1.90625 12.6064 2.29635 13.2965 2.98648C13.9866 3.67661 14.375 4.63044 14.375 5.625V9.375" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const EyeIcon = ({ onClick }: { onClick: () => void }) => (
  <svg
    className="password-toggle-icon"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    onClick={onClick}
    style={{ cursor: 'pointer' }}
  >
    <path d="M10 4.37501C3.75 4.37501 1.25 10 1.25 10C1.25 10 3.75 15.625 10 15.625C16.25 15.625 18.75 10 18.75 10C18.75 10 16.25 4.37501 10 4.37501Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);



const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, onSwitchToRegister }) => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const [formErrors, setFormErrors] = useState({
    email: '',
    password: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateEmail = (email: string): string => {
    if (!email.trim()) return 'El email es requerido';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'El formato del email no es válido';
    return '';
  };

  const validatePassword = (password: string): string => {
    if (!password) return 'La contraseña es requerida';
    if (password.length < 6) return 'La contraseña debe tener al menos 6 caracteres';
    return '';
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const emailError = validateEmail(email);
    const passwordError = validatePassword(password);

    setFormErrors({
      email: emailError,
      password: passwordError
    });

    if (!emailError && !passwordError) {
      try {
        await onSubmit(email, password);
      } catch (error) {
        console.error('Error en el inicio de sesión:', error);
      }
    }

    setIsSubmitting(false);
  };

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    if (formErrors.email) {
      setFormErrors(prev => ({
        ...prev,
        email: validateEmail(value)
      }));
    }
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setPassword(value);
    if (formErrors.password) {
      setFormErrors(prev => ({
        ...prev,
        password: validatePassword(value)
      }));
    }
  };



  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="auth-card"
    >
      <h2 className="auth-title">Iniciar Sesión</h2>
      <form onSubmit={handleSubmit}>
        <div className="input-group">
          <label className="input-label" htmlFor="email">
            Correo electrónico
          </label>
          <div className="input-container">
            <input
              id="email"
              type="email"
              className={`input-field with-icon ${
                formErrors.email ? 'error' :
                email && !formErrors.email ? 'valid' : ''
              }`}
              value={email}
              onChange={handleEmailChange}
              placeholder="<EMAIL>"
              autoComplete="email"
            />
            <EmailIcon />
          </div>
          <AnimatePresence>
            {formErrors.email && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="feedback error"
              >
                <span className="feedback-icon">⚠️</span>
                {formErrors.email}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <div className="input-group">
          <label className="input-label" htmlFor="password">
            Contraseña
          </label>
          <div className="input-container">
            <input
              id="password"
              type={showPassword ? "text" : "password"}
              className={`input-field with-icon ${password ? 'with-validation' : ''} ${
                formErrors.password ? 'error' :
                password && !formErrors.password ? 'valid' : ''
              }`}
              value={password}
              onChange={handlePasswordChange}
              placeholder="********"
              autoComplete="current-password"
            />
            <PasswordIcon />
            <div
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              <EyeIcon onClick={() => setShowPassword(!showPassword)} />
            </div>
          </div>

          <AnimatePresence>
            {formErrors.password && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="feedback error"
              >
                <span className="feedback-icon">⚠️</span>
                {formErrors.password}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          className="submit-button"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Iniciando sesión...' : 'Iniciar Sesión'}
        </motion.button>
      </form>

      <div className="switch-form-text">
        ¿No tienes una cuenta?
        <motion.span
          className="switch-form-link"
          onClick={onSwitchToRegister}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Regístrate aquí
        </motion.span>
      </div>
    </motion.div>
  );
};

export default LoginForm;