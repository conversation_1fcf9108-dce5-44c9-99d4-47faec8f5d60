import React, { useState, useEffect } from 'react';
import type { ChangeEvent, FormEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface RegisterFormProps {
  onSubmit: (name: string, email: string, password: string) => void;
  onSwitchToLogin: () => void;
}

// Iconos SVG para mejorar la interfaz
const UserIcon = () => (
  <svg className="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M16.6666 17.5V15.8333C16.6666 14.9493 16.3154 14.1014 15.6903 13.4763C15.0652 12.8512 14.2173 12.5 13.3333 12.5H6.66659C5.78253 12.5 4.93468 12.8512 4.30956 13.4763C3.68444 14.1014 3.33325 14.9493 3.33325 15.8333V17.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10.0001 9.16667C11.8411 9.16667 13.3334 7.67428 13.3334 5.83333C13.3334 3.99238 11.8411 2.5 10.0001 2.5C8.15913 2.5 6.66675 3.99238 6.66675 5.83333C6.66675 7.67428 8.15913 9.16667 10.0001 9.16667Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const EmailIcon = () => (
  <svg className="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M17.5 4.375H2.5C2.15833 4.375 1.875 4.65833 1.875 5V15C1.875 15.3417 2.15833 15.625 2.5 15.625H17.5C17.8417 15.625 18.125 15.3417 18.125 15V5C18.125 4.65833 17.8417 4.375 17.5 4.375Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M3.125 5.625L10 10.625L16.875 5.625" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const PasswordIcon = () => (
  <svg className="input-icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M15.625 9.375H4.375C3.68464 9.375 3.125 9.93464 3.125 10.625V16.25C3.125 16.9404 3.68464 17.5 4.375 17.5H15.625C16.3154 17.5 16.875 16.9404 16.875 16.25V10.625C16.875 9.93464 16.3154 9.375 15.625 9.375Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M5.625 9.375V5.625C5.625 4.63044 6.01339 3.67661 6.70352 2.98648C7.39365 2.29635 8.34748 1.90625 9.34204 1.90625H10.658C11.6525 1.90625 12.6064 2.29635 13.2965 2.98648C13.9866 3.67661 14.375 4.63044 14.375 5.625V9.375" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);

const EyeIcon = ({ onClick }: { onClick: () => void }) => (
  <svg
    className="password-toggle-icon"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    onClick={onClick}
    style={{ cursor: 'pointer' }}
  >
    <path d="M10 4.37501C3.75 4.37501 1.25 10 1.25 10C1.25 10 3.75 15.625 10 15.625C16.25 15.625 18.75 10 18.75 10C18.75 10 16.25 4.37501 10 4.37501Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
    <path d="M10 12.5C11.3807 12.5 12.5 11.3807 12.5 10C12.5 8.61929 11.3807 7.5 10 7.5C8.61929 7.5 7.5 8.61929 7.5 10C7.5 11.3807 8.61929 12.5 10 12.5Z" stroke="currentColor" strokeWidth="1.25" strokeLinecap="round" strokeLinejoin="round"/>
  </svg>
);



const RegisterForm: React.FC<RegisterFormProps> = ({ onSubmit, onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  
  const [formErrors, setFormErrors] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState<'weak' | 'medium' | 'strong' | ''>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const validateName = (name: string): string => {
    if (!name.trim()) return 'El nombre es requerido';
    if (name.length < 2) return 'El nombre debe tener al menos 2 caracteres';
    if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/.test(name)) return 'El nombre solo puede contener letras';
    return '';
  };

  const validateEmail = (email: string): string => {
    if (!email.trim()) return 'El email es requerido';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'El formato del email no es válido';
    return '';
  };

  const validatePassword = (password: string): string => {
    if (!password) return 'La contraseña es requerida';
    if (password.length < 8) return 'La contraseña debe tener al menos 8 caracteres';
    if (!/\d/.test(password)) return 'Debe incluir al menos un número';
    if (!/[A-Z]/.test(password)) return 'Debe incluir al menos una mayúscula';
    if (!/[a-z]/.test(password)) return 'Debe incluir al menos una minúscula';
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) return 'Debe incluir al menos un carácter especial';
    return '';
  };

  const validateConfirmPassword = (confirmPassword: string): string => {
    if (!confirmPassword) return 'Debes confirmar la contraseña';
    if (confirmPassword !== formData.password) return 'Las contraseñas no coinciden';
    return '';
  };

  const checkPasswordStrength = (password: string) => {
    if (!password) {
      setPasswordStrength('');
      return;
    }

    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) strength++;

    if (strength <= 2) setPasswordStrength('weak');
    else if (strength <= 4) setPasswordStrength('medium');
    else setPasswordStrength('strong');
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Validación en tiempo real
    let error = '';
    switch (name) {
      case 'name':
        error = validateName(value);
        break;
      case 'email':
        error = validateEmail(value);
        break;
      case 'password':
        error = validatePassword(value);
        checkPasswordStrength(value);
        break;
      case 'confirmPassword':
        error = validateConfirmPassword(value);
        break;
    }

    setFormErrors(prev => ({ ...prev, [name]: error }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const errors = {
      name: validateName(formData.name),
      email: validateEmail(formData.email),
      password: validatePassword(formData.password),
      confirmPassword: validateConfirmPassword(formData.confirmPassword)
    };

    setFormErrors(errors);

    if (!Object.values(errors).some(error => error !== '')) {
      try {
        await onSubmit(formData.name, formData.email, formData.password);
      } catch (error) {
        console.error('Error en el registro:', error);
      }
    }

    setIsSubmitting(false);
  };

  // Función para verificar requisitos individuales de contraseña
  const checkPasswordRequirement = (requirement: string, password: string) => {
    switch (requirement) {
      case 'length':
        return password.length >= 6;
      case 'uppercase':
        return /[A-Z]/.test(password);
      case 'lowercase':
        return /[a-z]/.test(password);
      case 'number':
        return /\d/.test(password);
      case 'special':
        return /[!@#$%^&*(),.?":{}|<>]/.test(password);
      default:
        return false;
    }
  };

  const renderPasswordRequirements = () => {
    const requirements = [
      { key: 'length', text: '6+ caracteres' },
      { key: 'uppercase', text: 'Mayúscula (A-Z)' },
      { key: 'lowercase', text: 'Minúscula (a-z)' },
      { key: 'number', text: 'Número (0-9)' },
      { key: 'special', text: 'Especial (!@#$)' }
    ];

    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="password-requirements"
      >
        <div className="password-requirements-title">
          Requisitos de contraseña:
        </div>
        <ul className="password-requirements-list">
          {requirements.map((req) => {
            const isMet = checkPasswordRequirement(req.key, formData.password);
            return (
              <motion.li
                key={req.key}
                className={`password-requirement ${isMet ? 'met' : 'not-met'}`}
                initial={{ opacity: 0.7 }}
                animate={{
                  opacity: 1,
                  color: isMet ? '#10b981' : '#6b7280'
                }}
                transition={{ duration: 0.2 }}
              >
                <span className="password-requirement-icon">
                  {isMet ? '✅' : '⭕'}
                </span>
                {req.text}
              </motion.li>
            );
          })}
        </ul>
      </motion.div>
    );
  };

  const renderPasswordStrength = () => {
    if (!formData.password) return null;

    const getStrengthColor = () => {
      switch (passwordStrength) {
        case 'weak': return '#ef4444';
        case 'medium': return '#f59e0b';
        case 'strong': return '#10b981';
        default: return '#e2e8f0';
      }
    };

    const getStrengthText = () => {
      switch (passwordStrength) {
        case 'weak': return 'Débil';
        case 'medium': return 'Media';
        case 'strong': return 'Fuerte';
        default: return '';
      }
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="password-strength"
      >
        <div className="password-strength-meter">
          <div className={`password-strength-meter-fill ${passwordStrength}`} />
        </div>
        <motion.div
          className="password-strength-text"
          style={{ color: getStrengthColor() }}
          initial={{ scale: 0.9 }}
          animate={{ scale: 1 }}
        >
          Seguridad: {getStrengthText()}
          {passwordStrength === 'strong' && (
            <motion.span
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              style={{ marginLeft: '0.5rem' }}
            >
              🔒
            </motion.span>
          )}
        </motion.div>
      </motion.div>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="auth-card register-card"
    >
      <h2 className="auth-title">Crear Cuenta</h2>
      <form onSubmit={handleSubmit}>
        <div className="register-form-grid">
          <div className="input-group">
          <label className="input-label" htmlFor="name">
            Nombre completo
          </label>
          <div className="input-container">
            <input
              id="name"
              name="name"
              type="text"
              className={`input-field with-icon ${
                formErrors.name ? 'error' :
                formData.name && !formErrors.name ? 'valid' : ''
              }`}
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Tu nombre"
              autoComplete="name"
            />
            <UserIcon />
          </div>
          <AnimatePresence>
            {formErrors.name && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="feedback error"
              >
                <span className="feedback-icon">⚠️</span>
                {formErrors.name}
              </motion.div>
            )}
          </AnimatePresence>
          </div>

          <div className="input-group">
            <label className="input-label" htmlFor="email">
              Correo electrónico
            </label>
            <div className="input-container">
              <input
                id="email"
                name="email"
                type="email"
                className={`input-field with-icon ${
                  formErrors.email ? 'error' :
                  formData.email && !formErrors.email ? 'valid' : ''
                }`}
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                autoComplete="email"
              />
              <EmailIcon />
            </div>
            <AnimatePresence>
              {formErrors.email && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="feedback error"
                >
                  <span className="feedback-icon">⚠️</span>
                  {formErrors.email}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        <div className="input-group register-form-full-width">
          <label className="input-label" htmlFor="password">
            Contraseña
          </label>
          <div className="input-container">
            <input
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              className={`input-field with-icon ${formData.password ? 'with-validation' : ''} ${
                formErrors.password ? 'error' :
                formData.password && !formErrors.password ? 'valid' : ''
              }`}
              value={formData.password}
              onChange={handleInputChange}
              placeholder="********"
              autoComplete="new-password"
            />
            <PasswordIcon />
            <div
              className="password-toggle"
              onClick={() => setShowPassword(!showPassword)}
            >
              <EyeIcon onClick={() => setShowPassword(!showPassword)} />
            </div>
          </div>
          {renderPasswordRequirements()}
          {renderPasswordStrength()}
          <AnimatePresence>
            {formErrors.password && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="feedback error"
              >
                <span className="feedback-icon">⚠️</span>
                {formErrors.password}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <div className="input-group register-form-full-width">
          <label className="input-label" htmlFor="confirmPassword">
            Confirmar contraseña
          </label>
          <div className="input-container">
            <input
              id="confirmPassword"
              name="confirmPassword"
              type={showConfirmPassword ? "text" : "password"}
              className={`input-field with-icon ${formData.confirmPassword ? 'with-validation' : ''} ${
                formErrors.confirmPassword ? 'error' :
                formData.confirmPassword && !formErrors.confirmPassword ? 'valid' : ''
              }`}
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="********"
              autoComplete="new-password"
            />
            <PasswordIcon />
            <div
              className="password-toggle"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              <EyeIcon onClick={() => setShowConfirmPassword(!showConfirmPassword)} />
            </div>
          </div>
          <AnimatePresence>
            {formErrors.confirmPassword && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="feedback error"
              >
                <span className="feedback-icon">⚠️</span>
                {formErrors.confirmPassword}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          className="submit-button"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Registrando...' : 'Crear cuenta'}
        </motion.button>
      </form>

      <div className="switch-form-text">
        ¿Ya tienes una cuenta?
        <motion.span
          className="switch-form-link"
          onClick={onSwitchToLogin}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Inicia sesión aquí
        </motion.span>
      </div>
    </motion.div>
  );
};

export default RegisterForm;