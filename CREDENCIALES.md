# 🔐 Sistema de Autenticación - GestiónIP con Supabase

## 🚀 Registro de Usuarios

**¡IMPORTANTE!** Ahora el sistema usa **Supabase** para autenticación real.

### 📝 Cómo Crear Usuarios de Prueba

1. **Regístrate normalmente** usando el formulario de registro
2. **Todos los nuevos usuarios** tienen rol `usuario` por defecto
3. **Para crear administradores:** Necesitas cambiar el rol manualmente en la base de datos

### 🔧 Crear Usuarios Administradores

Para crear usuarios con roles de administrador, puedes:

1. **Registrar usuario normal** primero
2. **Cambiar rol en Supabase Dashboard:**
   - Ve a: `https://supabase.com/dashboard/project/lwsmzajcafckotboifiy`
   - Tabla: `profiles`
   - Edita el campo `role` a: `administrador` o `super_administrador`

### 👥 Roles Disponibles

#### 🟢 Usuario (por defecto)
- **Permisos:** Acceso básico
  - ✅ Dashboard
  - ❌ Gestión de usuarios
  - ❌ Reportes y análisis
  - ❌ Configuración del sistema

#### 🔵 Administrador
- **Permisos:** Gestión operativa
  - ✅ Dashboard
  - ✅ Gestión de usuarios
  - ✅ Reportes y análisis
  - ❌ Configuración del sistema

#### 🔴 Super Administrador
- **Permisos:** Acceso completo al sistema
  - ✅ Dashboard
  - ✅ Gestión de usuarios
  - ✅ Reportes y análisis
  - ✅ Configuración del sistema

## 🚀 Cómo Probar

1. **Inicia la aplicación:**
   ```bash
   npm run dev
   ```

2. **Accede a:** `http://localhost:5174`

3. **Prueba diferentes roles:**
   - Inicia sesión con cualquiera de las credenciales arriba
   - Observa cómo cambian los permisos y opciones del menú
   - Cierra sesión y prueba con otro rol

## 🎯 Funcionalidades Implementadas

### ✅ Sistema de Autenticación con Supabase
- **Login real** con Supabase Auth
- **Registro de usuarios** con confirmación por email
- **Persistencia de sesión** automática con Supabase
- **Logout seguro** con limpieza de sesión
- **Gestión de perfiles** en base de datos PostgreSQL

### ✅ Sistema de Roles
- **3 roles:** Usuario, Administrador, Super Administrador
- **Permisos granulares** por funcionalidad
- **Rutas protegidas** según rol
- **UI adaptativa** según permisos

### ✅ Dashboard Administrativo
- **Sidebar dinámico** según permisos del usuario
- **Métricas y estadísticas** (datos demo)
- **Gestión de usuarios** (vista demo)
- **Reportes** (placeholder)
- **Configuración** (solo super admin)

### ✅ Seguridad
- Rutas protegidas con ProtectedRoute
- Verificación de roles en tiempo real
- Redirección automática según estado de auth
- Manejo de errores de autenticación

## 🔧 Arquitectura Técnica

### **Stack Completo:**
- ⚛️ **Frontend:** React 19 + TypeScript
- 🗄️ **Backend:** Supabase (PostgreSQL + Auth)
- 🎨 **Animaciones:** Framer Motion
- 🛣️ **Routing:** React Router DOM
- 🎯 **Estado:** Context API + Supabase Auth
- 💅 **Estilos:** CSS personalizado moderno
- 🔐 **Autenticación:** Supabase Auth + RLS

### **Estructura de Archivos:**
```
src/
├── components/
│   ├── auth/
│   │   ├── AuthContainer.tsx
│   │   ├── LoginForm.tsx
│   │   ├── RegisterForm.tsx
│   │   └── ProtectedRoute.tsx
│   └── dashboard/
│       └── AdminDashboard.tsx
├── contexts/
│   └── AuthContext.tsx
├── lib/
│   └── supabase.ts
├── types/
│   └── auth.ts
└── App.tsx
```

### **Base de Datos (Supabase):**
```sql
-- Tabla profiles
CREATE TABLE profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  role user_role DEFAULT 'usuario',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enum de roles
CREATE TYPE user_role AS ENUM (
  'usuario',
  'administrador',
  'super_administrador'
);

-- RLS (Row Level Security) habilitado
-- Políticas de seguridad por rol
```

## 🎨 Características de UI/UX

### ✨ Diseño Moderno
- **Glassmorphism** y efectos visuales
- **Gradientes suaves** y sombras elegantes
- **Animaciones fluidas** con Framer Motion
- **Tipografía profesional** (Inter + Poppins)

### 📱 Responsive Design
- **Mobile-first** approach
- **Grid adaptativo** para formularios
- **Sidebar colapsible** en móviles
- **Notificaciones** posicionadas correctamente

### 🎯 Validación Inteligente
- **Validación en tiempo real** mientras escribes
- **Feedback visual** con colores de borde
- **Requisitos de contraseña** en formato compacto
- **Mensajes de error** claros y útiles

## 🔄 Próximas Mejoras

- [x] ~~Integración con backend real~~ ✅ **Completado con Supabase**
- [ ] Gestión completa de usuarios (CRUD) en el dashboard
- [ ] Sistema de permisos más granular
- [ ] Reportes con datos reales desde Supabase
- [ ] Configuraciones del sistema
- [ ] Logs de auditoría con triggers
- [ ] Notificaciones en tiempo real (Supabase Realtime)
- [ ] Tema oscuro/claro
- [ ] Confirmación de email automática
- [ ] Recuperación de contraseña
- [ ] Autenticación con proveedores (Google, GitHub)

---

**¡Disfruta probando el sistema! 🚀**
